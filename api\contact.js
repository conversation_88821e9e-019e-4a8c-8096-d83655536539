// api/contact.js - Vercel serverless function for contact form

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  const { name, company, email, challenge } = req.body;

  // Basic validation
  if (!name || !company || !email) {
    return res.status(400).json({
      success: false,
      message: 'Nom, entreprise et email sont requis'
    });
  }

  // Email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return res.status(400).json({
      success: false,
      message: 'Format d\'email invalide'
    });
  }

  try {
    // For now, just log the submission and return success
    // This allows the form to work while we set up email configuration
    console.log('Contact form submission:', {
      name,
      company,
      email,
      challenge,
      timestamp: new Date().toISOString()
    });

    // Simulate successful submission
    return res.status(200).json({
      success: true,
      message: 'Merci! Votre demande a été reçue. Nous vous contacterons bientôt.'
    });

  } catch (error) {
    console.error('Error processing contact form:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors du traitement de votre demande. Veuillez réessayer.'
    });
  }
}