// api/contact.js - Place this file in an 'api' folder at your project root

const nodemailer = require('nodemailer');

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version');

  // <PERSON>le preflight requests
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  const { name, company, email, challenge } = req.body;

  // Basic validation
  if (!name || !company || !email) {
    return res.status(400).json({ 
      success: false, 
      message: 'Nom, entreprise et email sont requis' 
    });
  }

  // Email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return res.status(400).json({ 
      success: false, 
      message: 'Format d\'email invalide' 
    });
  }

  try {
    // Check if environment variables are set
    if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
      console.error('Missing environment variables');
      return res.status(500).json({ 
        success: false, 
        message: 'Configuration du serveur manquante' 
      });
    }

    // Configure email transporter
    const transporter = nodemailer.createTransporter({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });

    // Test the connection
    await transporter.verify();

    // Email content to you
    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: process.env.EMAIL_USER, // Send to yourself
      subject: `Nouvelle demande de consultation - ${company}`,
      html: `
        <h2>Nouvelle demande de consultation</h2>
        <p><strong>Nom:</strong> ${name}</p>
        <p><strong>Entreprise:</strong> ${company}</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Défi principal:</strong></p>
        <p>${challenge || 'Non spécifié'}</p>
        
        <hr>
        <p><em>Message envoyé depuis le site IMPAXX.CA</em></p>
      `,
      replyTo: email
    };

    // Send main email
    await transporter.sendMail(mailOptions);

    // Send confirmation email to client
    const confirmationOptions = {
      from: process.env.EMAIL_USER,
      to: email,
      subject: 'Confirmation de votre demande - IMPAXX',
      html: `
        <h2>Merci pour votre demande, ${name}!</h2>
        <p>Nous avons bien reçu votre demande de consultation pour <strong>${company}</strong>.</p>
        <p>Nous vous contacterons dans les plus brefs délais.</p>
        <br>
        <p>Cordialement,<br>L'équipe IMPAXX</p>
      `
    };

    await transporter.sendMail(confirmationOptions);

    return res.status(200).json({ 
      success: true, 
      message: 'Message envoyé avec succès!' 
    });

  } catch (error) {
    console.error('Error sending email:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Erreur lors de l\'envoi du message. Veuillez réessayer.' 
    });
  }
}